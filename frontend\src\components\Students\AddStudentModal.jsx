import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { X, Save, User, Users } from 'lucide-react';
import { classesAPI, studentsAPI } from '../../services/api';
import toast from 'react-hot-toast';

// Validation schema
const studentSchema = z.object({
  class_id: z.string().min(1, '<PERSON>ınıf seçimi gerekli'),
  student_number: z.string().min(1, 'Öğrenci numarası gerekli'),
  first_name: z.string().min(1, 'Ad gerekli'),
  last_name: z.string().min(1, 'Soyad gerekli'),
  date_of_birth: z.string().optional().or(z.literal('')),
  gender: z.enum(['male', 'female']).optional().or(z.literal('')),
  address: z.string().optional().or(z.literal('')),
  parent_first_name: z.string().min(1, '<PERSON>eli adı gerekli'),
  parent_last_name: z.string().min(1, '<PERSON>eli soyadı gerekli'),
  parent_phone: z.string().min(10, 'Geçerli telefon numarası gerekli'),
  parent_email: z.string().email('Geçerli e-posta adresi girin').optional().or(z.literal('')),
  parent_relationship: z.enum(['mother', 'father', 'guardian']).default('mother'),
  parent_address: z.string().optional().or(z.literal(''))
});

const AddStudentModal = ({ isOpen, onClose, onStudentAdded }) => {
  const [classes, setClasses] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch
  } = useForm({
    resolver: zodResolver(studentSchema),
    defaultValues: {
      gender: '',
      parent_relationship: 'mother'
    }
  });

  // Load classes
  useEffect(() => {
    if (isOpen) {
      loadClasses();
    }
  }, [isOpen]);

  const loadClasses = async () => {
    try {
      const response = await classesAPI.getAll();
      if (response.success) {
        setClasses(response.data.classes);
      }
    } catch (error) {
      console.error('Error loading classes:', error);
      toast.error('Sınıflar yüklenirken hata oluştu');
    }
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Clean up empty string values
      const cleanedData = { ...data };

      // Convert empty strings to undefined for optional fields
      if (cleanedData.date_of_birth === '') cleanedData.date_of_birth = undefined;
      if (cleanedData.gender === '') cleanedData.gender = undefined;
      if (cleanedData.address === '') cleanedData.address = undefined;
      if (cleanedData.parent_email === '') cleanedData.parent_email = undefined;
      if (cleanedData.parent_address === '') cleanedData.parent_address = undefined;

      console.log('Submitting student data:', cleanedData);

      const result = await studentsAPI.create(cleanedData);

      if (result.success) {
        toast.success('Öğrenci başarıyla eklendi!');
        reset();
        onStudentAdded();
        onClose();
      } else {
        toast.error(result.message || 'Öğrenci eklenirken hata oluştu');
      }
    } catch (error) {
      console.error('Error creating student:', error);

      // Show more specific error message if available
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else if (error.response?.data?.errors) {
        toast.error(error.response.data.errors.join(', '));
      } else {
        toast.error('Öğrenci eklenirken hata oluştu');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Yeni Öğrenci Ekle
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
          {/* Student Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Öğrenci Bilgileri</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sınıf *
                </label>
                <select
                  {...register('class_id')}
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.class_id ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Sınıf seçin</option>
                  {classes.map((cls) => (
                    <option key={cls.id} value={cls.id}>
                      {cls.name} ({cls.student_count} öğrenci)
                    </option>
                  ))}
                </select>
                {errors.class_id && (
                  <p className="mt-1 text-sm text-red-600">{errors.class_id.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Öğrenci Numarası *
                </label>
                <input
                  {...register('student_number')}
                  type="text"
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.student_number ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Örn: 9A001"
                />
                {errors.student_number && (
                  <p className="mt-1 text-sm text-red-600">{errors.student_number.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ad *
                </label>
                <input
                  {...register('first_name')}
                  type="text"
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.first_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.first_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.first_name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Soyad *
                </label>
                <input
                  {...register('last_name')}
                  type="text"
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.last_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.last_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.last_name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Doğum Tarihi
                </label>
                <input
                  {...register('date_of_birth')}
                  type="date"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Cinsiyet
                </label>
                <select
                  {...register('gender')}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Seçin</option>
                  <option value="male">Erkek</option>
                  <option value="female">Kız</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Adres
              </label>
              <textarea
                {...register('address')}
                rows="2"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Öğrenci adresi"
              />
            </div>
          </div>

          {/* Parent Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Veli Bilgileri</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Veli Adı *
                </label>
                <input
                  {...register('parent_first_name')}
                  type="text"
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.parent_first_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.parent_first_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.parent_first_name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Veli Soyadı *
                </label>
                <input
                  {...register('parent_last_name')}
                  type="text"
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.parent_last_name ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.parent_last_name && (
                  <p className="mt-1 text-sm text-red-600">{errors.parent_last_name.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Telefon *
                </label>
                <input
                  {...register('parent_phone')}
                  type="tel"
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.parent_phone ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="05XX XXX XX XX"
                />
                {errors.parent_phone && (
                  <p className="mt-1 text-sm text-red-600">{errors.parent_phone.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  E-posta
                </label>
                <input
                  {...register('parent_email')}
                  type="email"
                  className={`w-full border rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.parent_email ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="<EMAIL>"
                />
                {errors.parent_email && (
                  <p className="mt-1 text-sm text-red-600">{errors.parent_email.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Yakınlık
                </label>
                <select
                  {...register('parent_relationship')}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="mother">Anne</option>
                  <option value="father">Baba</option>
                  <option value="guardian">Vasi</option>
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Veli Adresi
              </label>
              <textarea
                {...register('parent_address')}
                rows="2"
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Veli adresi (boş bırakılırsa öğrenci adresi kullanılır)"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              disabled={isSubmitting}
            >
              İptal
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Kaydediliyor...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Kaydet
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddStudentModal;
