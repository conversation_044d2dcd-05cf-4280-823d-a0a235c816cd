import axios from 'axios';

const testAPI = async () => {
  try {
    console.log('🧪 Testing login API...');

    const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    });

    console.log('✅ Login successful!');
    const token = loginResponse.data.data.token;
    console.log('Token:', token);

    // Test classes API
    console.log('\n🧪 Testing classes API...');
    const classesResponse = await axios.get('http://localhost:3000/api/classes', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Classes fetched successfully!');
    console.log('Classes:', classesResponse.data.data.classes);

    if (classesResponse.data.data.classes.length > 0) {
      const classId = classesResponse.data.data.classes[0].id;

      // Test student creation
      console.log('\n🧪 Testing student creation API...');
      const studentData = {
        class_id: classId,
        student_number: 'TEST001',
        first_name: 'Test',
        last_name: '<PERSON><PERSON><PERSON><PERSON>',
        date_of_birth: '2010-01-01',
        gender: 'male',
        address: 'Test Adresi',
        parent_first_name: 'Test',
        parent_last_name: 'Veli',
        parent_phone: '05321234567',
        parent_email: '<EMAIL>',
        parent_relationship: 'father',
        parent_address: 'Test Veli Adresi'
      };

      const studentResponse = await axios.post('http://localhost:3000/api/students', studentData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('✅ Student created successfully!');
      console.log('Student:', studentResponse.data);
    }

  } catch (error) {
    console.error('❌ Test failed!');
    console.error('Error:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Full error response:', JSON.stringify(error.response.data, null, 2));
    }
  }
};

testAPI();
