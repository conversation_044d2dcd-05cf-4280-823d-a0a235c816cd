import { Student, Class, Parent, School } from '../models/index.js';
import { Op } from 'sequelize';
import smsService from '../services/smsService.js';
import Joi from 'joi';
import multer from 'multer';
import csv from 'csv-parser';
import fs from 'fs';
import path from 'path';

// Validation schemas
const searchSchema = Joi.object({
  q: Joi.string().min(1).max(100).optional(),
  class_id: Joi.string().uuid().optional(),
  grade_level: Joi.number().integer().min(1).max(12).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20)
});

const createStudentSchema = Joi.object({
  class_id: Joi.string().required(),
  student_number: Joi.string().min(1).max(50).required(),
  first_name: Joi.string().min(1).max(100).required(),
  last_name: Joi.string().min(1).max(100).required(),
  date_of_birth: Joi.date().optional(),
  gender: Joi.string().valid('male', 'female').optional(),
  address: Joi.string().max(500).optional(),
  parent_first_name: Joi.string().min(1).max(100).required(),
  parent_last_name: Joi.string().min(1).max(100).required(),
  parent_phone: Joi.string().min(10).max(20).required(),
  parent_email: Joi.string().email().optional(),
  parent_relationship: Joi.string().valid('mother', 'father', 'guardian').default('mother'),
  parent_address: Joi.string().max(500).optional()
});

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files are allowed'), false);
    }
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Get all students with pagination and filtering
export const getStudents = async (req, res) => {
  try {
    const { error, value } = searchSchema.validate(req.query);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const { q, class_id, grade_level, page, limit } = value;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = {
      school_id: req.user.school_id,
      is_active: true
    };

    // Add search conditions
    if (q) {
      whereConditions[Op.or] = [
        { first_name: { [Op.iLike]: `%${q}%` } },
        { last_name: { [Op.iLike]: `%${q}%` } },
        { student_number: { [Op.iLike]: `%${q}%` } }
      ];
    }

    if (class_id) {
      whereConditions.class_id = class_id;
    }

    // Include conditions for class filtering
    const includeConditions = [
      {
        model: Class,
        attributes: ['id', 'name', 'grade_level', 'section'],
        where: grade_level ? { grade_level } : undefined
      },
      {
        model: Parent,
        attributes: ['id', 'first_name', 'last_name', 'phone', 'relationship', 'is_primary_contact']
      }
    ];

    // Get students with pagination
    const { count, rows: students } = await Student.findAndCountAll({
      where: whereConditions,
      include: includeConditions,
      limit,
      offset,
      order: [['first_name', 'ASC'], ['last_name', 'ASC']],
      distinct: true
    });

    const totalPages = Math.ceil(count / limit);

    res.json({
      success: true,
      data: {
        students,
        pagination: {
          current_page: page,
          total_pages: totalPages,
          total_count: count,
          per_page: limit,
          has_next: page < totalPages,
          has_prev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get students error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch students'
    });
  }
};

// Get student by ID
export const getStudentById = async (req, res) => {
  try {
    const { id } = req.params;

    const student = await Student.findOne({
      where: {
        id,
        school_id: req.user.school_id,
        is_active: true
      },
      include: [
        {
          model: Class,
          attributes: ['id', 'name', 'grade_level', 'section', 'academic_year']
        },
        {
          model: Parent,
          attributes: ['id', 'first_name', 'last_name', 'phone', 'email', 'relationship', 'is_primary_contact']
        }
      ]
    });

    if (!student) {
      return res.status(404).json({
        success: false,
        message: 'Student not found'
      });
    }

    res.json({
      success: true,
      data: { student }
    });

  } catch (error) {
    console.error('Get student by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch student'
    });
  }
};

// Search students (quick search)
export const searchStudents = async (req, res) => {
  try {
    const { q } = req.query;

    if (!q || q.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters'
      });
    }

    const students = await Student.findAll({
      where: {
        school_id: req.user.school_id,
        is_active: true,
        [Op.or]: [
          { first_name: { [Op.iLike]: `%${q}%` } },
          { last_name: { [Op.iLike]: `%${q}%` } },
          { student_number: { [Op.iLike]: `%${q}%` } }
        ]
      },
      include: [
        {
          model: Class,
          attributes: ['id', 'name', 'grade_level', 'section']
        }
      ],
      limit: 10,
      order: [['first_name', 'ASC'], ['last_name', 'ASC']]
    });

    res.json({
      success: true,
      data: { students }
    });

  } catch (error) {
    console.error('Search students error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed'
    });
  }
};

// Get students by class
export const getStudentsByClass = async (req, res) => {
  try {
    const { classId } = req.params;

    // Verify class belongs to user's school
    const classExists = await Class.findOne({
      where: {
        id: classId,
        school_id: req.user.school_id
      }
    });

    if (!classExists) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    const students = await Student.findAll({
      where: {
        class_id: classId,
        is_active: true
      },
      include: [
        {
          model: Parent,
          attributes: ['id', 'first_name', 'last_name', 'phone', 'relationship', 'is_primary_contact']
        }
      ],
      order: [['first_name', 'ASC'], ['last_name', 'ASC']]
    });

    res.json({
      success: true,
      data: { students }
    });

  } catch (error) {
    console.error('Get students by class error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch class students'
    });
  }
};

// Create new student
export const createStudent = async (req, res) => {
  try {
    const { error, value } = createStudentSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        errors: error.details.map(detail => detail.message)
      });
    }

    const {
      class_id,
      student_number,
      first_name,
      last_name,
      date_of_birth,
      gender,
      address,
      parent_first_name,
      parent_last_name,
      parent_phone,
      parent_email,
      parent_relationship,
      parent_address
    } = value;

    // Check if class exists and belongs to user's school
    const classExists = await Class.findOne({
      where: {
        id: class_id,
        school_id: req.user.school_id
      }
    });

    if (!classExists) {
      return res.status(404).json({
        success: false,
        message: 'Class not found'
      });
    }

    // Check if student number already exists
    const existingStudent = await Student.findOne({
      where: {
        student_number,
        school_id: req.user.school_id
      }
    });

    if (existingStudent) {
      return res.status(409).json({
        success: false,
        message: 'Student number already exists'
      });
    }

    // Create student
    const student = await Student.create({
      school_id: req.user.school_id,
      class_id,
      student_number,
      first_name,
      last_name,
      date_of_birth,
      gender,
      address,
      enrollment_date: new Date()
    });

    // Create parent
    const parent = await Parent.create({
      student_id: student.id,
      first_name: parent_first_name,
      last_name: parent_last_name,
      relationship: parent_relationship,
      phone: parent_phone,
      email: parent_email,
      address: parent_address || address,
      is_primary_contact: true
    });

    // Fetch created student with relations
    const createdStudent = await Student.findByPk(student.id, {
      include: [
        {
          model: Class,
          attributes: ['id', 'name', 'grade_level', 'section']
        },
        {
          model: Parent,
          attributes: ['id', 'first_name', 'last_name', 'phone', 'relationship']
        }
      ]
    });

    // Send welcome SMS to parent
    let smsResult = null;
    try {
      const welcomeMessage = `Merhaba ${parent_first_name} ${parent_last_name}, çocuğunuz ${first_name} ${last_name} Lucy for School sistemine başarıyla kaydedildi. Öğrenci No: ${student_number}. Nakipoğlu Cumhuriyet Anadolu Lisesi`;

      smsResult = await smsService.sendSingle(parent_phone, welcomeMessage);

      if (smsResult.success) {
        console.log(`✅ Welcome SMS sent to ${parent_phone} for student ${student_number}`);
      } else {
        console.error(`❌ Failed to send welcome SMS to ${parent_phone}:`, smsResult.error);
      }
    } catch (smsError) {
      console.error('SMS sending error:', smsError);
    }

    res.status(201).json({
      success: true,
      message: smsResult?.success
        ? 'Öğrenci başarıyla kaydedildi ve veliye SMS gönderildi'
        : 'Öğrenci başarıyla kaydedildi ancak SMS gönderilemedi',
      data: {
        student: createdStudent,
        sms: smsResult ? {
          sent: smsResult.success,
          error: smsResult.error || null
        } : null
      }
    });

  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create student'
    });
  }
};

// CSV Import students
export const importStudentsCSV = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'CSV file is required'
      });
    }

    const results = [];
    const errors = [];
    let processedCount = 0;
    let successCount = 0;

    // Read and parse CSV file
    const csvData = await new Promise((resolve, reject) => {
      const data = [];
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on('data', (row) => data.push(row))
        .on('end', () => resolve(data))
        .on('error', reject);
    });

    console.log('📊 CSV Data received:', csvData.length, 'rows');

    for (const row of csvData) {
      processedCount++;

      try {
        // Validate required fields
        const requiredFields = [
          'student_number', 'first_name', 'last_name', 'class_name',
          'parent_first_name', 'parent_last_name', 'parent_phone'
        ];

        const missingFields = requiredFields.filter(field => !row[field] || row[field].trim() === '');

        if (missingFields.length > 0) {
          errors.push({
            row: processedCount,
            error: `Missing required fields: ${missingFields.join(', ')}`,
            data: row
          });
          continue;
        }

        // Find class by name
        const targetClass = await Class.findOne({
          where: {
            name: row.class_name.trim(),
            school_id: req.user.school_id
          }
        });

        if (!targetClass) {
          errors.push({
            row: processedCount,
            error: `Class '${row.class_name}' not found`,
            data: row
          });
          continue;
        }

        // Check if student number already exists
        const existingStudent = await Student.findOne({
          where: {
            student_number: row.student_number.trim(),
            school_id: req.user.school_id
          }
        });

        if (existingStudent) {
          errors.push({
            row: processedCount,
            error: `Student number '${row.student_number}' already exists`,
            data: row
          });
          continue;
        }

        // Create student
        const student = await Student.create({
          school_id: req.user.school_id,
          class_id: targetClass.id,
          student_number: row.student_number.trim(),
          first_name: row.first_name.trim(),
          last_name: row.last_name.trim(),
          date_of_birth: row.date_of_birth ? new Date(row.date_of_birth) : null,
          gender: row.gender && ['male', 'female'].includes(row.gender.toLowerCase()) ? row.gender.toLowerCase() : null,
          address: row.address ? row.address.trim() : null,
          enrollment_date: new Date()
        });

        // Create parent
        await Parent.create({
          student_id: student.id,
          first_name: row.parent_first_name.trim(),
          last_name: row.parent_last_name.trim(),
          relationship: row.parent_relationship && ['mother', 'father', 'guardian'].includes(row.parent_relationship.toLowerCase())
            ? row.parent_relationship.toLowerCase() : 'mother',
          phone: row.parent_phone.trim(),
          email: row.parent_email ? row.parent_email.trim() : null,
          address: row.parent_address ? row.parent_address.trim() : (row.address ? row.address.trim() : null),
          is_primary_contact: true
        });

        successCount++;
        results.push({
          row: processedCount,
          student_number: row.student_number.trim(),
          name: `${row.first_name.trim()} ${row.last_name.trim()}`,
          class: row.class_name.trim(),
          status: 'success'
        });

      } catch (rowError) {
        console.error(`Error processing row ${processedCount}:`, rowError);
        errors.push({
          row: processedCount,
          error: rowError.message,
          data: row
        });
      }
    }

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json({
      success: true,
      message: `CSV import completed. ${successCount} students imported successfully.`,
      data: {
        total_rows: processedCount,
        successful_imports: successCount,
        failed_imports: errors.length,
        results,
        errors: errors.slice(0, 10) // Limit errors to first 10
      }
    });

  } catch (error) {
    console.error('CSV import error:', error);

    // Clean up uploaded file if it exists
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    res.status(500).json({
      success: false,
      message: 'CSV import failed'
    });
  }
};

// Export multer upload middleware
export const uploadCSV = upload.single('csvFile');
